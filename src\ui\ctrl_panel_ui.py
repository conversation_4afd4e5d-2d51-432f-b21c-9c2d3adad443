# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src\ui\ctrl_panel.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1094, 731)
        self.gridLayout_3 = QtWidgets.QGridLayout(Form)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.CardWidget = CardWidget(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CardWidget.sizePolicy().hasHeightForWidth())
        self.CardWidget.setSizePolicy(sizePolicy)
        self.CardWidget.setObjectName("CardWidget")
        self.gridLayout = QtWidgets.QGridLayout(self.CardWidget)
        self.gridLayout.setObjectName("gridLayout")
        self.label_vbus_in_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_vbus_in_2.sizePolicy().hasHeightForWidth())
        self.label_vbus_in_2.setSizePolicy(sizePolicy)
        self.label_vbus_in_2.setMinimumSize(QtCore.QSize(80, 0))
        self.label_vbus_in_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_vbus_in_2.setProperty("lightColor", QtGui.QColor(255, 5, 30))
        self.label_vbus_in_2.setObjectName("label_vbus_in_2")
        self.gridLayout.addWidget(self.label_vbus_in_2, 0, 0, 1, 1)
        self.label_vbus_in = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_vbus_in.sizePolicy().hasHeightForWidth())
        self.label_vbus_in.setSizePolicy(sizePolicy)
        self.label_vbus_in.setText("")
        self.label_vbus_in.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_vbus_in.setProperty("lightColor", QtGui.QColor(255, 5, 30))
        self.label_vbus_in.setObjectName("label_vbus_in")
        self.gridLayout.addWidget(self.label_vbus_in, 0, 1, 1, 1)
        self.label_vdc_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_vdc_2.sizePolicy().hasHeightForWidth())
        self.label_vdc_2.setSizePolicy(sizePolicy)
        self.label_vdc_2.setMinimumSize(QtCore.QSize(50, 0))
        self.label_vdc_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_vdc_2.setProperty("lightColor", QtGui.QColor(255, 5, 30))
        self.label_vdc_2.setObjectName("label_vdc_2")
        self.gridLayout.addWidget(self.label_vdc_2, 0, 2, 1, 1)
        self.label_vdc = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_vdc.sizePolicy().hasHeightForWidth())
        self.label_vdc.setSizePolicy(sizePolicy)
        self.label_vdc.setText("")
        self.label_vdc.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_vdc.setProperty("lightColor", QtGui.QColor(255, 5, 30))
        self.label_vdc.setObjectName("label_vdc")
        self.gridLayout.addWidget(self.label_vdc, 0, 3, 1, 1)
        self.label_ud_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ud_2.sizePolicy().hasHeightForWidth())
        self.label_ud_2.setSizePolicy(sizePolicy)
        self.label_ud_2.setMinimumSize(QtCore.QSize(80, 0))
        self.label_ud_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ud_2.setProperty("lightColor", QtGui.QColor(213, 110, 7))
        self.label_ud_2.setObjectName("label_ud_2")
        self.gridLayout.addWidget(self.label_ud_2, 1, 0, 1, 1)
        self.label_ud = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ud.sizePolicy().hasHeightForWidth())
        self.label_ud.setSizePolicy(sizePolicy)
        self.label_ud.setMinimumSize(QtCore.QSize(0, 0))
        self.label_ud.setText("")
        self.label_ud.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ud.setProperty("lightColor", QtGui.QColor(213, 110, 7))
        self.label_ud.setObjectName("label_ud")
        self.gridLayout.addWidget(self.label_ud, 1, 1, 1, 1)
        self.label_ibus_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ibus_2.sizePolicy().hasHeightForWidth())
        self.label_ibus_2.setSizePolicy(sizePolicy)
        self.label_ibus_2.setMinimumSize(QtCore.QSize(50, 0))
        self.label_ibus_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ibus_2.setProperty("lightColor", QtGui.QColor(255, 5, 30))
        self.label_ibus_2.setObjectName("label_ibus_2")
        self.gridLayout.addWidget(self.label_ibus_2, 1, 2, 1, 1)
        self.label_ibus = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ibus.sizePolicy().hasHeightForWidth())
        self.label_ibus.setSizePolicy(sizePolicy)
        self.label_ibus.setText("")
        self.label_ibus.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ibus.setProperty("lightColor", QtGui.QColor(255, 5, 30))
        self.label_ibus.setObjectName("label_ibus")
        self.gridLayout.addWidget(self.label_ibus, 1, 3, 1, 1)
        self.label_uq_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_uq_2.sizePolicy().hasHeightForWidth())
        self.label_uq_2.setSizePolicy(sizePolicy)
        self.label_uq_2.setMinimumSize(QtCore.QSize(80, 0))
        self.label_uq_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_uq_2.setProperty("lightColor", QtGui.QColor(213, 0, 39))
        self.label_uq_2.setObjectName("label_uq_2")
        self.gridLayout.addWidget(self.label_uq_2, 2, 0, 1, 1)
        self.label_uq = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_uq.sizePolicy().hasHeightForWidth())
        self.label_uq.setSizePolicy(sizePolicy)
        self.label_uq.setText("")
        self.label_uq.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_uq.setProperty("lightColor", QtGui.QColor(213, 0, 39))
        self.label_uq.setObjectName("label_uq")
        self.gridLayout.addWidget(self.label_uq, 2, 1, 1, 1)
        self.label_ia_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ia_2.sizePolicy().hasHeightForWidth())
        self.label_ia_2.setSizePolicy(sizePolicy)
        self.label_ia_2.setMinimumSize(QtCore.QSize(50, 0))
        self.label_ia_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ia_2.setProperty("lightColor", QtGui.QColor(225, 229, 0))
        self.label_ia_2.setObjectName("label_ia_2")
        self.gridLayout.addWidget(self.label_ia_2, 2, 2, 1, 1)
        self.label_ia = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ia.sizePolicy().hasHeightForWidth())
        self.label_ia.setSizePolicy(sizePolicy)
        self.label_ia.setText("")
        self.label_ia.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ia.setProperty("lightColor", QtGui.QColor(225, 229, 0))
        self.label_ia.setObjectName("label_ia")
        self.gridLayout.addWidget(self.label_ia, 2, 3, 1, 1)
        self.label_id_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_id_2.sizePolicy().hasHeightForWidth())
        self.label_id_2.setSizePolicy(sizePolicy)
        self.label_id_2.setMinimumSize(QtCore.QSize(80, 0))
        self.label_id_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_id_2.setProperty("lightColor", QtGui.QColor(213, 110, 7))
        self.label_id_2.setObjectName("label_id_2")
        self.gridLayout.addWidget(self.label_id_2, 3, 0, 1, 1)
        self.label_id = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_id.sizePolicy().hasHeightForWidth())
        self.label_id.setSizePolicy(sizePolicy)
        self.label_id.setText("")
        self.label_id.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_id.setProperty("lightColor", QtGui.QColor(213, 110, 7))
        self.label_id.setObjectName("label_id")
        self.gridLayout.addWidget(self.label_id, 3, 1, 1, 1)
        self.label_ib_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ib_2.sizePolicy().hasHeightForWidth())
        self.label_ib_2.setSizePolicy(sizePolicy)
        self.label_ib_2.setMinimumSize(QtCore.QSize(50, 0))
        self.label_ib_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ib_2.setProperty("lightColor", QtGui.QColor(78, 200, 12))
        self.label_ib_2.setObjectName("label_ib_2")
        self.gridLayout.addWidget(self.label_ib_2, 3, 2, 1, 1)
        self.label_ib = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ib.sizePolicy().hasHeightForWidth())
        self.label_ib.setSizePolicy(sizePolicy)
        self.label_ib.setText("")
        self.label_ib.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ib.setProperty("lightColor", QtGui.QColor(78, 200, 12))
        self.label_ib.setObjectName("label_ib")
        self.gridLayout.addWidget(self.label_ib, 3, 3, 1, 1)
        self.label_iq_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_iq_2.sizePolicy().hasHeightForWidth())
        self.label_iq_2.setSizePolicy(sizePolicy)
        self.label_iq_2.setMinimumSize(QtCore.QSize(80, 0))
        self.label_iq_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_iq_2.setProperty("lightColor", QtGui.QColor(213, 110, 7))
        self.label_iq_2.setObjectName("label_iq_2")
        self.gridLayout.addWidget(self.label_iq_2, 4, 0, 1, 1)
        self.label_iq = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_iq.sizePolicy().hasHeightForWidth())
        self.label_iq.setSizePolicy(sizePolicy)
        self.label_iq.setText("")
        self.label_iq.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_iq.setProperty("lightColor", QtGui.QColor(213, 110, 7))
        self.label_iq.setObjectName("label_iq")
        self.gridLayout.addWidget(self.label_iq, 4, 1, 1, 1)
        self.label_ic_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ic_2.sizePolicy().hasHeightForWidth())
        self.label_ic_2.setSizePolicy(sizePolicy)
        self.label_ic_2.setMinimumSize(QtCore.QSize(50, 0))
        self.label_ic_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ic_2.setProperty("lightColor", QtGui.QColor(255, 5, 30))
        self.label_ic_2.setObjectName("label_ic_2")
        self.gridLayout.addWidget(self.label_ic_2, 4, 2, 1, 1)
        self.label_ic = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_ic.sizePolicy().hasHeightForWidth())
        self.label_ic.setSizePolicy(sizePolicy)
        self.label_ic.setText("")
        self.label_ic.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_ic.setProperty("lightColor", QtGui.QColor(255, 5, 30))
        self.label_ic.setObjectName("label_ic")
        self.gridLayout.addWidget(self.label_ic, 4, 3, 1, 1)
        self.gridLayout_3.addWidget(self.CardWidget, 1, 0, 1, 1)
        self.widget_2 = QtWidgets.QWidget(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.widget_2.sizePolicy().hasHeightForWidth())
        self.widget_2.setSizePolicy(sizePolicy)
        self.widget_2.setMinimumSize(QtCore.QSize(0, 80))
        self.widget_2.setObjectName("widget_2")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.widget_2)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.label_online_status = SubtitleLabel(self.widget_2)
        self.label_online_status.setObjectName("label_online_status")
        self.gridLayout_4.addWidget(self.label_online_status, 0, 1, 1, 1)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_4.addItem(spacerItem, 1, 1, 1, 1)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_4.addItem(spacerItem1, 1, 3, 1, 1)
        self.badge_online_status = IconInfoBadge(self.widget_2)
        self.badge_online_status.setMinimumSize(QtCore.QSize(22, 22))
        self.badge_online_status.setMaximumSize(QtCore.QSize(22, 22))
        self.badge_online_status.setObjectName("badge_online_status")
        self.gridLayout_4.addWidget(self.badge_online_status, 0, 0, 1, 1)
        self.LargeTitleLabel = LargeTitleLabel(self.widget_2)
        font = QtGui.QFont()
        font.setFamily("华文行楷")
        font.setPointSize(44)
        font.setBold(False)
        font.setWeight(50)
        self.LargeTitleLabel.setFont(font)
        self.LargeTitleLabel.setObjectName("LargeTitleLabel")
        self.gridLayout_4.addWidget(self.LargeTitleLabel, 0, 2, 2, 1)
        self.gridLayout_3.addWidget(self.widget_2, 0, 0, 1, 3)
        self.CardWidget_3 = CardWidget(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CardWidget_3.sizePolicy().hasHeightForWidth())
        self.CardWidget_3.setSizePolicy(sizePolicy)
        self.CardWidget_3.setObjectName("CardWidget_3")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.CardWidget_3)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.btn_reset_dev = PillPushButton(self.CardWidget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_reset_dev.sizePolicy().hasHeightForWidth())
        self.btn_reset_dev.setSizePolicy(sizePolicy)
        self.btn_reset_dev.setMinimumSize(QtCore.QSize(121, 121))
        font = QtGui.QFont()
        font.setPointSize(22)
        font.setBold(True)
        font.setWeight(75)
        self.btn_reset_dev.setFont(font)
        self.btn_reset_dev.setCheckable(False)
        self.btn_reset_dev.setProperty("hasIcon", False)
        self.btn_reset_dev.setObjectName("btn_reset_dev")
        self.horizontalLayout_3.addWidget(self.btn_reset_dev)
        self.btn_launch_dev = PillPushButton(self.CardWidget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_launch_dev.sizePolicy().hasHeightForWidth())
        self.btn_launch_dev.setSizePolicy(sizePolicy)
        self.btn_launch_dev.setMinimumSize(QtCore.QSize(121, 121))
        font = QtGui.QFont()
        font.setPointSize(22)
        font.setBold(True)
        font.setWeight(75)
        self.btn_launch_dev.setFont(font)
        self.btn_launch_dev.setCheckable(True)
        self.btn_launch_dev.setObjectName("btn_launch_dev")
        self.horizontalLayout_3.addWidget(self.btn_launch_dev)
        self.btn_stop_dev = PillPushButton(self.CardWidget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_stop_dev.sizePolicy().hasHeightForWidth())
        self.btn_stop_dev.setSizePolicy(sizePolicy)
        self.btn_stop_dev.setMinimumSize(QtCore.QSize(121, 121))
        font = QtGui.QFont()
        font.setPointSize(22)
        font.setBold(True)
        font.setWeight(75)
        self.btn_stop_dev.setFont(font)
        self.btn_stop_dev.setCheckable(True)
        self.btn_stop_dev.setChecked(False)
        self.btn_stop_dev.setObjectName("btn_stop_dev")
        self.horizontalLayout_3.addWidget(self.btn_stop_dev)
        self.btn_calibrate_dev = PillPushButton(self.CardWidget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_calibrate_dev.sizePolicy().hasHeightForWidth())
        self.btn_calibrate_dev.setSizePolicy(sizePolicy)
        self.btn_calibrate_dev.setMinimumSize(QtCore.QSize(121, 121))
        font = QtGui.QFont()
        font.setPointSize(22)
        font.setBold(True)
        font.setWeight(75)
        self.btn_calibrate_dev.setFont(font)
        self.btn_calibrate_dev.setCheckable(False)
        self.btn_calibrate_dev.setObjectName("btn_calibrate_dev")
        self.horizontalLayout_3.addWidget(self.btn_calibrate_dev)
        self.gridLayout_3.addWidget(self.CardWidget_3, 2, 0, 1, 3)
        self.CardWidget_2 = CardWidget(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CardWidget_2.sizePolicy().hasHeightForWidth())
        self.CardWidget_2.setSizePolicy(sizePolicy)
        self.CardWidget_2.setObjectName("CardWidget_2")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.CardWidget_2)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSizeConstraint(QtWidgets.QLayout.SetFixedSize)
        self.horizontalLayout.setContentsMargins(-1, 0, -1, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label_speed = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed.sizePolicy().hasHeightForWidth())
        self.label_speed.setSizePolicy(sizePolicy)
        self.label_speed.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed.setObjectName("label_speed")
        self.horizontalLayout.addWidget(self.label_speed)
        self.SubtitleLabel_3 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SubtitleLabel_3.sizePolicy().hasHeightForWidth())
        self.SubtitleLabel_3.setSizePolicy(sizePolicy)
        self.SubtitleLabel_3.setMinimumSize(QtCore.QSize(0, 0))
        self.SubtitleLabel_3.setObjectName("SubtitleLabel_3")
        self.horizontalLayout.addWidget(self.SubtitleLabel_3)
        self.spinbox_speed = DoubleSpinBox(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinbox_speed.sizePolicy().hasHeightForWidth())
        self.spinbox_speed.setSizePolicy(sizePolicy)
        self.spinbox_speed.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_speed.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(16)
        font.setBold(False)
        font.setWeight(50)
        font.setKerning(True)
        font.setStyleStrategy(QtGui.QFont.PreferAntialias)
        self.spinbox_speed.setFont(font)
        self.spinbox_speed.setFrame(True)
        self.spinbox_speed.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinbox_speed.setReadOnly(False)
        self.spinbox_speed.setButtonSymbols(QtWidgets.QAbstractSpinBox.NoButtons)
        self.spinbox_speed.setProperty("showGroupSeparator", False)
        self.spinbox_speed.setPrefix("")
        self.spinbox_speed.setMinimum(-3000.0)
        self.spinbox_speed.setMaximum(3000.0)
        self.spinbox_speed.setSingleStep(10.0)
        self.spinbox_speed.setStepType(QtWidgets.QAbstractSpinBox.DefaultStepType)
        self.spinbox_speed.setObjectName("spinbox_speed")
        self.horizontalLayout.addWidget(self.spinbox_speed)
        self.btn_speed_set = PushButton(self.CardWidget_2)
        self.btn_speed_set.setMinimumSize(QtCore.QSize(0, 44))
        font = QtGui.QFont()
        font.setPointSize(16)
        font.setBold(True)
        font.setWeight(75)
        font.setStyleStrategy(QtGui.QFont.PreferAntialias)
        self.btn_speed_set.setFont(font)
        self.btn_speed_set.setObjectName("btn_speed_set")
        self.horizontalLayout.addWidget(self.btn_speed_set)
        self.spinbox_current = DoubleSpinBox(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinbox_current.sizePolicy().hasHeightForWidth())
        self.spinbox_current.setSizePolicy(sizePolicy)
        self.spinbox_current.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_current.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(16)
        font.setBold(False)
        font.setWeight(50)
        font.setKerning(True)
        font.setStyleStrategy(QtGui.QFont.PreferAntialias)
        self.spinbox_current.setFont(font)
        self.spinbox_current.setInputMethodHints(QtCore.Qt.ImhFormattedNumbersOnly)
        self.spinbox_current.setFrame(True)
        self.spinbox_current.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.spinbox_current.setReadOnly(False)
        self.spinbox_current.setButtonSymbols(QtWidgets.QAbstractSpinBox.NoButtons)
        self.spinbox_current.setSpecialValueText("")
        self.spinbox_current.setProperty("showGroupSeparator", False)
        self.spinbox_current.setPrefix("")
        self.spinbox_current.setMinimum(-500.0)
        self.spinbox_current.setMaximum(500.0)
        self.spinbox_current.setSingleStep(1.0)
        self.spinbox_current.setStepType(QtWidgets.QAbstractSpinBox.DefaultStepType)
        self.spinbox_current.setObjectName("spinbox_current")
        self.horizontalLayout.addWidget(self.spinbox_current)
        self.btn_current_set = PushButton(self.CardWidget_2)
        self.btn_current_set.setMinimumSize(QtCore.QSize(0, 44))
        font = QtGui.QFont()
        font.setPointSize(16)
        font.setBold(True)
        font.setWeight(75)
        font.setStyleStrategy(QtGui.QFont.PreferAntialias)
        self.btn_current_set.setFont(font)
        self.btn_current_set.setObjectName("btn_current_set")
        self.horizontalLayout.addWidget(self.btn_current_set)
        self.sw_btn_host_computer = SwitchButton(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.sw_btn_host_computer.sizePolicy().hasHeightForWidth())
        self.sw_btn_host_computer.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(False)
        font.setWeight(50)
        self.sw_btn_host_computer.setFont(font)
        self.sw_btn_host_computer.setChecked(True)
        self.sw_btn_host_computer.setObjectName("sw_btn_host_computer")
        self.horizontalLayout.addWidget(self.sw_btn_host_computer)
        self.gridLayout_2.addLayout(self.horizontalLayout, 0, 0, 1, 3)
        self.HorizontalSeparator = HorizontalSeparator(self.CardWidget_2)
        self.HorizontalSeparator.setObjectName("HorizontalSeparator")
        self.gridLayout_2.addWidget(self.HorizontalSeparator, 2, 0, 1, 3)
        self.widget_3 = QtWidgets.QWidget(self.CardWidget_2)
        self.widget_3.setObjectName("widget_3")
        self.gridLayout_5 = QtWidgets.QGridLayout(self.widget_3)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.label_2 = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_2.setObjectName("label_2")
        self.gridLayout_5.addWidget(self.label_2, 0, 0, 1, 1)
        self.label_angle = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_angle.sizePolicy().hasHeightForWidth())
        self.label_angle.setSizePolicy(sizePolicy)
        self.label_angle.setText("")
        self.label_angle.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_angle.setObjectName("label_angle")
        self.gridLayout_5.addWidget(self.label_angle, 1, 0, 1, 1)
        self.label_1 = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_1.sizePolicy().hasHeightForWidth())
        self.label_1.setSizePolicy(sizePolicy)
        self.label_1.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_1.setObjectName("label_1")
        self.gridLayout_5.addWidget(self.label_1, 2, 0, 1, 1)
        self.label_duty_cycle = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_duty_cycle.sizePolicy().hasHeightForWidth())
        self.label_duty_cycle.setSizePolicy(sizePolicy)
        self.label_duty_cycle.setText("")
        self.label_duty_cycle.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_duty_cycle.setObjectName("label_duty_cycle")
        self.gridLayout_5.addWidget(self.label_duty_cycle, 3, 0, 1, 1)
        self.label_4 = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        self.label_4.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_4.setObjectName("label_4")
        self.gridLayout_5.addWidget(self.label_4, 4, 0, 1, 1)
        self.label_temperature = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_temperature.sizePolicy().hasHeightForWidth())
        self.label_temperature.setSizePolicy(sizePolicy)
        self.label_temperature.setText("")
        self.label_temperature.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_temperature.setObjectName("label_temperature")
        self.gridLayout_5.addWidget(self.label_temperature, 5, 0, 1, 1)
        self.label_3 = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_3.setObjectName("label_3")
        self.gridLayout_5.addWidget(self.label_3, 6, 0, 1, 1)
        self.label_motor_temp = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_motor_temp.sizePolicy().hasHeightForWidth())
        self.label_motor_temp.setSizePolicy(sizePolicy)
        self.label_motor_temp.setText("")
        self.label_motor_temp.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_motor_temp.setObjectName("label_motor_temp")
        self.gridLayout_5.addWidget(self.label_motor_temp, 7, 0, 1, 1)
        self.label_sys_status_2 = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_sys_status_2.sizePolicy().hasHeightForWidth())
        self.label_sys_status_2.setSizePolicy(sizePolicy)
        self.label_sys_status_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_sys_status_2.setObjectName("label_sys_status_2")
        self.gridLayout_5.addWidget(self.label_sys_status_2, 8, 0, 1, 1)
        self.label_sys_status = SubtitleLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_sys_status.sizePolicy().hasHeightForWidth())
        self.label_sys_status.setSizePolicy(sizePolicy)
        self.label_sys_status.setText("")
        self.label_sys_status.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_sys_status.setObjectName("label_sys_status")
        self.gridLayout_5.addWidget(self.label_sys_status, 9, 0, 1, 1)
        self.gridLayout_2.addWidget(self.widget_3, 3, 0, 1, 1)
        self.VerticalSeparator = VerticalSeparator(self.CardWidget_2)
        self.VerticalSeparator.setObjectName("VerticalSeparator")
        self.gridLayout_2.addWidget(self.VerticalSeparator, 3, 1, 1, 1)
        self.widget = QtWidgets.QWidget(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.widget.sizePolicy().hasHeightForWidth())
        self.widget.setSizePolicy(sizePolicy)
        self.widget.setObjectName("widget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.widget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label_dev_error = SubtitleLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_dev_error.sizePolicy().hasHeightForWidth())
        self.label_dev_error.setSizePolicy(sizePolicy)
        self.label_dev_error.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_dev_error.setObjectName("label_dev_error")
        self.verticalLayout.addWidget(self.label_dev_error)
        self.list_dev_error = ListWidget(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.list_dev_error.sizePolicy().hasHeightForWidth())
        self.list_dev_error.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("0xProto Nerd Font")
        font.setPointSize(12)
        self.list_dev_error.setFont(font)
        self.list_dev_error.setObjectName("list_dev_error")
        item = QtWidgets.QListWidgetItem()
        self.list_dev_error.addItem(item)
        self.verticalLayout.addWidget(self.list_dev_error)
        self.label_fault_status = SubtitleLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_fault_status.sizePolicy().hasHeightForWidth())
        self.label_fault_status.setSizePolicy(sizePolicy)
        self.label_fault_status.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_fault_status.setObjectName("label_fault_status")
        self.verticalLayout.addWidget(self.label_fault_status)
        self.list_fault_status = ListWidget(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.list_fault_status.sizePolicy().hasHeightForWidth())
        self.list_fault_status.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("0xProto Nerd Font")
        font.setPointSize(12)
        self.list_fault_status.setFont(font)
        self.list_fault_status.setObjectName("list_fault_status")
        item = QtWidgets.QListWidgetItem()
        self.list_fault_status.addItem(item)
        self.verticalLayout.addWidget(self.list_fault_status)
        self.gridLayout_2.addWidget(self.widget, 3, 2, 1, 1)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.label_speed_2 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_2.sizePolicy().hasHeightForWidth())
        self.label_speed_2.setSizePolicy(sizePolicy)
        self.label_speed_2.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_2.setObjectName("label_speed_2")
        self.horizontalLayout_4.addWidget(self.label_speed_2)
        self.radio_btn_speed_1 = RadioButton(self.CardWidget_2)
        self.radio_btn_speed_1.setObjectName("radio_btn_speed_1")
        self.horizontalLayout_4.addWidget(self.radio_btn_speed_1)
        self.radio_btn_speed_10 = RadioButton(self.CardWidget_2)
        self.radio_btn_speed_10.setChecked(True)
        self.radio_btn_speed_10.setObjectName("radio_btn_speed_10")
        self.horizontalLayout_4.addWidget(self.radio_btn_speed_10)
        self.radio_btn_speed_100 = RadioButton(self.CardWidget_2)
        self.radio_btn_speed_100.setObjectName("radio_btn_speed_100")
        self.horizontalLayout_4.addWidget(self.radio_btn_speed_100)
        self.gridLayout_2.addLayout(self.horizontalLayout_4, 1, 0, 1, 3)
        self.gridLayout_3.addWidget(self.CardWidget_2, 1, 1, 1, 2)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_vbus_in_2.setText(_translate("Form", "Vbus_in:"))
        self.label_vdc_2.setText(_translate("Form", "Vdc:"))
        self.label_vdc_2.setProperty("lightCustomQss", _translate("Form", "FluentLabelBase{color:#000000}"))
        self.label_vdc.setProperty("lightCustomQss", _translate("Form", "FluentLabelBase{color:#000000}"))
        self.label_ud_2.setText(_translate("Form", "Ud:"))
        self.label_ibus_2.setText(_translate("Form", "Ibus:"))
        self.label_uq_2.setText(_translate("Form", "Uq:"))
        self.label_ia_2.setText(_translate("Form", "Ia:"))
        self.label_id_2.setText(_translate("Form", "Id:"))
        self.label_ib_2.setText(_translate("Form", "Ib:"))
        self.label_iq_2.setText(_translate("Form", "Iq:"))
        self.label_ic_2.setText(_translate("Form", "Ic:"))
        self.label_online_status.setText(_translate("Form", "以太网离线"))
        self.badge_online_status.setProperty("level", _translate("Form", "Error"))
        self.LargeTitleLabel.setText(_translate("Form", "电机控制器"))
        self.btn_reset_dev.setText(_translate("Form", "复位"))
        self.btn_launch_dev.setText(_translate("Form", "启动"))
        self.btn_stop_dev.setText(_translate("Form", "停止"))
        self.btn_calibrate_dev.setText(_translate("Form", "校准"))
        self.label_speed.setText(_translate("Form", "转速："))
        self.SubtitleLabel_3.setText(_translate("Form", "rpm"))
        self.spinbox_speed.setSuffix(_translate("Form", "  rpm"))
        self.btn_speed_set.setText(_translate("Form", "设置转速"))
        self.spinbox_current.setSuffix(_translate("Form", " A"))
        self.btn_current_set.setText(_translate("Form", "设置电流"))
        self.sw_btn_host_computer.setText(_translate("Form", "上位机开启"))
        self.sw_btn_host_computer.setOnText(_translate("Form", "上位机开启"))
        self.sw_btn_host_computer.setOffText(_translate("Form", "上位机关闭"))
        self.label_2.setText(_translate("Form", "角度："))
        self.label_1.setText(_translate("Form", "占空比："))
        self.label_4.setText(_translate("Form", "逆变温度："))
        self.label_3.setText(_translate("Form", "电机温度："))
        self.label_sys_status_2.setText(_translate("Form", "系统状态："))
        self.label_dev_error.setText(_translate("Form", "报错："))
        __sortingEnabled = self.list_dev_error.isSortingEnabled()
        self.list_dev_error.setSortingEnabled(False)
        item = self.list_dev_error.item(0)
        item.setText(_translate("Form", "无"))
        self.list_dev_error.setSortingEnabled(__sortingEnabled)
        self.label_fault_status.setText(_translate("Form", "故障状态："))
        __sortingEnabled = self.list_fault_status.isSortingEnabled()
        self.list_fault_status.setSortingEnabled(False)
        item = self.list_fault_status.item(0)
        item.setText(_translate("Form", "无故障"))
        self.list_fault_status.setSortingEnabled(__sortingEnabled)
        self.label_speed_2.setText(_translate("Form", "速度设置步伐："))
        self.radio_btn_speed_1.setText(_translate("Form", "x1"))
        self.radio_btn_speed_10.setText(_translate("Form", "x10"))
        self.radio_btn_speed_100.setText(_translate("Form", "x100"))
from qfluentwidgets import CardWidget, DoubleSpinBox, HorizontalSeparator, IconInfoBadge, LargeTitleLabel, ListWidget, PillPushButton, PushButton, RadioButton, SubtitleLabel, SwitchButton, VerticalSeparator
