<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>668</width>
    <height>742</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="1">
    <widget class="LargeTitleLabel" name="LargeTitleLabel">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>华文行楷</family>
       <pointsize>44</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>电机控制器</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="3">
    <widget class="CardWidget" name="CardWidget">
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="SubtitleLabel" name="label_1">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>本机IP地址：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="SubtitleLabel" name="label_pc_ip">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>***********</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="LineEdit" name="line_edit_pc_ip">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="maxLength">
         <number>15</number>
        </property>
        <property name="placeholderText">
         <string>***********</string>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="ToolButton" name="btn_pc_ip_set">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="SubtitleLabel" name="label_3">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>本机端口：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="SubtitleLabel" name="label_pc_port">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>16011</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="SpinBox" name="spinbox_pc_port">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="minimum">
         <number>1000</number>
        </property>
        <property name="maximum">
         <number>65535</number>
        </property>
        <property name="value">
         <number>16011</number>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="ToolButton" name="btn_pc_port_set">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="SubtitleLabel" name="label_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>设备IP地址：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="SubtitleLabel" name="label_device_ip">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>***********</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
        </property>
       </widget>
      </item>
      <item row="2" column="3">
       <widget class="LineEdit" name="line_edit_device_ip">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="maxLength">
         <number>15</number>
        </property>
        <property name="placeholderText">
         <string>***********00</string>
        </property>
       </widget>
      </item>
      <item row="2" column="4">
       <widget class="ToolButton" name="btn_device_ip_set">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="SubtitleLabel" name="label_4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>设备端口：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="SubtitleLabel" name="label_device_port">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>16011</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
        </property>
       </widget>
      </item>
      <item row="3" column="3">
       <widget class="SpinBox" name="spinbox_device_port">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="minimum">
         <number>1000</number>
        </property>
        <property name="maximum">
         <number>65535</number>
        </property>
        <property name="value">
         <number>16011</number>
        </property>
       </widget>
      </item>
      <item row="3" column="4">
       <widget class="ToolButton" name="btn_device_port_set">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="4" column="0" colspan="2">
       <widget class="SubtitleLabel" name="label_8">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>设备子网掩码：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="4" column="2">
       <widget class="SubtitleLabel" name="label_device_ip_mask">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>*************</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
        </property>
       </widget>
      </item>
      <item row="4" column="3">
       <widget class="LineEdit" name="line_edit_device_ip_mask">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>*************</string>
        </property>
        <property name="maxLength">
         <number>15</number>
        </property>
       </widget>
      </item>
      <item row="4" column="4">
       <widget class="ToolButton" name="btn_device_ip_mask_set">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="SubtitleLabel" name="label_6">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>设备别名：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="5" column="2">
       <widget class="SubtitleLabel" name="label_device_name">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>一号驱动器</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="5" column="3">
       <widget class="LineEdit" name="line_edit_device_name">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="inputMask">
         <string/>
        </property>
        <property name="text">
         <string>一号驱动器</string>
        </property>
       </widget>
      </item>
      <item row="5" column="4">
       <widget class="ToolButton" name="btn_device_name_set">
        <property name="minimumSize">
         <size>
          <width>44</width>
          <height>44</height>
         </size>
        </property>
        <property name="icon">
         <iconset>
          <normaloff>../../img/save2.svg</normaloff>../../img/save2.svg</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="SubtitleLabel" name="label_5">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>设备UID：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="6" column="2">
       <widget class="SubtitleLabel" name="label_device_uid">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Sunken</enum>
        </property>
        <property name="text">
         <string>0x12345678</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="indent">
         <number>-1</number>
        </property>
        <property name="openExternalLinks">
         <bool>false</bool>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
        </property>
       </widget>
      </item>
      <item row="8" column="0" colspan="2">
       <widget class="SubtitleLabel" name="label_9">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>设备MAC地址：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="10" column="0" colspan="2">
       <widget class="SubtitleLabel" name="label_7">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>142</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>永久保存参数：</string>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
       </widget>
      </item>
      <item row="10" column="2">
       <widget class="PillPushButton" name="btn_save_net_param">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>44</height>
         </size>
        </property>
        <property name="text">
         <string>保存网络参数命令</string>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="8" column="2">
       <widget class="SubtitleLabel" name="label_device_mac_addr">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="textFormat">
         <enum>Qt::MarkdownText</enum>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PushButton</class>
   <extends>QPushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>PillPushButton</class>
   <extends>ToggleButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ToolButton</class>
   <extends>QToolButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>ToggleButton</class>
   <extends>PushButton</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>CardWidget</class>
   <extends>QFrame</extends>
   <header>qfluentwidgets</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SubtitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>LargeTitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>LineEdit</class>
   <extends>QLineEdit</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>SpinBox</class>
   <extends>QSpinBox</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
