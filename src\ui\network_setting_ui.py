# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src\ui\network_setting.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(668, 742)
        self.gridLayout_2 = QtWidgets.QGridLayout(Form)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.LargeTitleLabel = LargeTitleLabel(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.LargeTitleLabel.sizePolicy().hasHeightForWidth())
        self.LargeTitleLabel.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("华文行楷")
        font.setPointSize(44)
        font.setBold(False)
        font.setWeight(50)
        self.LargeTitleLabel.setFont(font)
        self.LargeTitleLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.LargeTitleLabel.setObjectName("LargeTitleLabel")
        self.gridLayout_2.addWidget(self.LargeTitleLabel, 0, 1, 1, 1)
        self.CardWidget = CardWidget(Form)
        self.CardWidget.setObjectName("CardWidget")
        self.gridLayout = QtWidgets.QGridLayout(self.CardWidget)
        self.gridLayout.setObjectName("gridLayout")
        self.label_1 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_1.sizePolicy().hasHeightForWidth())
        self.label_1.setSizePolicy(sizePolicy)
        self.label_1.setMinimumSize(QtCore.QSize(142, 0))
        self.label_1.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_1.setObjectName("label_1")
        self.gridLayout.addWidget(self.label_1, 0, 0, 1, 1)
        self.label_pc_ip = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_pc_ip.sizePolicy().hasHeightForWidth())
        self.label_pc_ip.setSizePolicy(sizePolicy)
        self.label_pc_ip.setMinimumSize(QtCore.QSize(0, 0))
        self.label_pc_ip.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_pc_ip.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByMouse|QtCore.Qt.TextSelectableByMouse)
        self.label_pc_ip.setObjectName("label_pc_ip")
        self.gridLayout.addWidget(self.label_pc_ip, 0, 2, 1, 1)
        self.line_edit_pc_ip = LineEdit(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.line_edit_pc_ip.sizePolicy().hasHeightForWidth())
        self.line_edit_pc_ip.setSizePolicy(sizePolicy)
        self.line_edit_pc_ip.setMinimumSize(QtCore.QSize(0, 44))
        self.line_edit_pc_ip.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.line_edit_pc_ip.setText("")
        self.line_edit_pc_ip.setMaxLength(15)
        self.line_edit_pc_ip.setObjectName("line_edit_pc_ip")
        self.gridLayout.addWidget(self.line_edit_pc_ip, 0, 3, 1, 1)
        self.btn_pc_ip_set = ToolButton(self.CardWidget)
        self.btn_pc_ip_set.setMinimumSize(QtCore.QSize(44, 44))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap("src\\ui\\../../img/save2.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_pc_ip_set.setIcon(icon)
        self.btn_pc_ip_set.setIconSize(QtCore.QSize(22, 22))
        self.btn_pc_ip_set.setObjectName("btn_pc_ip_set")
        self.gridLayout.addWidget(self.btn_pc_ip_set, 0, 4, 1, 1)
        self.label_3 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setMinimumSize(QtCore.QSize(142, 0))
        self.label_3.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 1, 0, 1, 1)
        self.label_pc_port = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_pc_port.sizePolicy().hasHeightForWidth())
        self.label_pc_port.setSizePolicy(sizePolicy)
        self.label_pc_port.setMinimumSize(QtCore.QSize(0, 0))
        self.label_pc_port.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_pc_port.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByMouse|QtCore.Qt.TextSelectableByMouse)
        self.label_pc_port.setObjectName("label_pc_port")
        self.gridLayout.addWidget(self.label_pc_port, 1, 2, 1, 1)
        self.spinbox_pc_port = SpinBox(self.CardWidget)
        self.spinbox_pc_port.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_pc_port.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_pc_port.setMinimum(1000)
        self.spinbox_pc_port.setMaximum(65535)
        self.spinbox_pc_port.setProperty("value", 16011)
        self.spinbox_pc_port.setObjectName("spinbox_pc_port")
        self.gridLayout.addWidget(self.spinbox_pc_port, 1, 3, 1, 1)
        self.btn_pc_port_set = ToolButton(self.CardWidget)
        self.btn_pc_port_set.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_pc_port_set.setIcon(icon)
        self.btn_pc_port_set.setIconSize(QtCore.QSize(22, 22))
        self.btn_pc_port_set.setObjectName("btn_pc_port_set")
        self.gridLayout.addWidget(self.btn_pc_port_set, 1, 4, 1, 1)
        self.label_2 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setMinimumSize(QtCore.QSize(142, 0))
        self.label_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 2, 0, 1, 1)
        self.label_device_ip = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_device_ip.sizePolicy().hasHeightForWidth())
        self.label_device_ip.setSizePolicy(sizePolicy)
        self.label_device_ip.setMinimumSize(QtCore.QSize(0, 0))
        self.label_device_ip.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_device_ip.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByMouse|QtCore.Qt.TextSelectableByMouse)
        self.label_device_ip.setObjectName("label_device_ip")
        self.gridLayout.addWidget(self.label_device_ip, 2, 2, 1, 1)
        self.line_edit_device_ip = LineEdit(self.CardWidget)
        self.line_edit_device_ip.setMinimumSize(QtCore.QSize(0, 44))
        self.line_edit_device_ip.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.line_edit_device_ip.setMaxLength(15)
        self.line_edit_device_ip.setObjectName("line_edit_device_ip")
        self.gridLayout.addWidget(self.line_edit_device_ip, 2, 3, 1, 1)
        self.btn_device_ip_set = ToolButton(self.CardWidget)
        self.btn_device_ip_set.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_device_ip_set.setIcon(icon)
        self.btn_device_ip_set.setIconSize(QtCore.QSize(22, 22))
        self.btn_device_ip_set.setObjectName("btn_device_ip_set")
        self.gridLayout.addWidget(self.btn_device_ip_set, 2, 4, 1, 1)
        self.label_4 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        self.label_4.setMinimumSize(QtCore.QSize(142, 0))
        self.label_4.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 3, 0, 1, 1)
        self.label_device_port = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_device_port.sizePolicy().hasHeightForWidth())
        self.label_device_port.setSizePolicy(sizePolicy)
        self.label_device_port.setMinimumSize(QtCore.QSize(0, 0))
        self.label_device_port.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_device_port.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByMouse|QtCore.Qt.TextSelectableByMouse)
        self.label_device_port.setObjectName("label_device_port")
        self.gridLayout.addWidget(self.label_device_port, 3, 2, 1, 1)
        self.spinbox_device_port = SpinBox(self.CardWidget)
        self.spinbox_device_port.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_device_port.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_device_port.setMinimum(1000)
        self.spinbox_device_port.setMaximum(65535)
        self.spinbox_device_port.setProperty("value", 16011)
        self.spinbox_device_port.setObjectName("spinbox_device_port")
        self.gridLayout.addWidget(self.spinbox_device_port, 3, 3, 1, 1)
        self.btn_device_port_set = ToolButton(self.CardWidget)
        self.btn_device_port_set.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_device_port_set.setIcon(icon)
        self.btn_device_port_set.setIconSize(QtCore.QSize(22, 22))
        self.btn_device_port_set.setObjectName("btn_device_port_set")
        self.gridLayout.addWidget(self.btn_device_port_set, 3, 4, 1, 1)
        self.label_8 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_8.sizePolicy().hasHeightForWidth())
        self.label_8.setSizePolicy(sizePolicy)
        self.label_8.setMinimumSize(QtCore.QSize(142, 0))
        self.label_8.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_8.setObjectName("label_8")
        self.gridLayout.addWidget(self.label_8, 4, 0, 1, 2)
        self.label_device_ip_mask = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_device_ip_mask.sizePolicy().hasHeightForWidth())
        self.label_device_ip_mask.setSizePolicy(sizePolicy)
        self.label_device_ip_mask.setMinimumSize(QtCore.QSize(0, 0))
        self.label_device_ip_mask.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_device_ip_mask.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByMouse|QtCore.Qt.TextSelectableByMouse)
        self.label_device_ip_mask.setObjectName("label_device_ip_mask")
        self.gridLayout.addWidget(self.label_device_ip_mask, 4, 2, 1, 1)
        self.line_edit_device_ip_mask = LineEdit(self.CardWidget)
        self.line_edit_device_ip_mask.setMinimumSize(QtCore.QSize(0, 44))
        self.line_edit_device_ip_mask.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.line_edit_device_ip_mask.setMaxLength(15)
        self.line_edit_device_ip_mask.setObjectName("line_edit_device_ip_mask")
        self.gridLayout.addWidget(self.line_edit_device_ip_mask, 4, 3, 1, 1)
        self.btn_device_ip_mask_set = ToolButton(self.CardWidget)
        self.btn_device_ip_mask_set.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_device_ip_mask_set.setIcon(icon)
        self.btn_device_ip_mask_set.setIconSize(QtCore.QSize(22, 22))
        self.btn_device_ip_mask_set.setObjectName("btn_device_ip_mask_set")
        self.gridLayout.addWidget(self.btn_device_ip_mask_set, 4, 4, 1, 1)
        self.label_6 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy)
        self.label_6.setMinimumSize(QtCore.QSize(142, 0))
        self.label_6.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_6.setObjectName("label_6")
        self.gridLayout.addWidget(self.label_6, 5, 0, 1, 1)
        self.label_device_name = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_device_name.sizePolicy().hasHeightForWidth())
        self.label_device_name.setSizePolicy(sizePolicy)
        self.label_device_name.setMinimumSize(QtCore.QSize(0, 0))
        self.label_device_name.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_device_name.setObjectName("label_device_name")
        self.gridLayout.addWidget(self.label_device_name, 5, 2, 1, 1)
        self.line_edit_device_name = LineEdit(self.CardWidget)
        self.line_edit_device_name.setMinimumSize(QtCore.QSize(0, 44))
        self.line_edit_device_name.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.line_edit_device_name.setInputMask("")
        self.line_edit_device_name.setObjectName("line_edit_device_name")
        self.gridLayout.addWidget(self.line_edit_device_name, 5, 3, 1, 1)
        self.btn_device_name_set = ToolButton(self.CardWidget)
        self.btn_device_name_set.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_device_name_set.setIcon(icon)
        self.btn_device_name_set.setIconSize(QtCore.QSize(22, 22))
        self.btn_device_name_set.setObjectName("btn_device_name_set")
        self.gridLayout.addWidget(self.btn_device_name_set, 5, 4, 1, 1)
        self.label_5 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        self.label_5.setMinimumSize(QtCore.QSize(142, 0))
        self.label_5.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 6, 0, 1, 1)
        self.label_device_uid = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_device_uid.sizePolicy().hasHeightForWidth())
        self.label_device_uid.setSizePolicy(sizePolicy)
        self.label_device_uid.setMinimumSize(QtCore.QSize(0, 0))
        self.label_device_uid.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_device_uid.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.label_device_uid.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_device_uid.setIndent(-1)
        self.label_device_uid.setOpenExternalLinks(False)
        self.label_device_uid.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByMouse|QtCore.Qt.TextSelectableByMouse)
        self.label_device_uid.setObjectName("label_device_uid")
        self.gridLayout.addWidget(self.label_device_uid, 6, 2, 1, 1)
        self.label_9 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_9.sizePolicy().hasHeightForWidth())
        self.label_9.setSizePolicy(sizePolicy)
        self.label_9.setMinimumSize(QtCore.QSize(142, 0))
        self.label_9.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_9.setObjectName("label_9")
        self.gridLayout.addWidget(self.label_9, 8, 0, 1, 2)
        self.label_7 = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_7.sizePolicy().hasHeightForWidth())
        self.label_7.setSizePolicy(sizePolicy)
        self.label_7.setMinimumSize(QtCore.QSize(142, 0))
        self.label_7.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 10, 0, 1, 2)
        self.btn_save_net_param = PillPushButton(self.CardWidget)
        self.btn_save_net_param.setMinimumSize(QtCore.QSize(0, 44))
        self.btn_save_net_param.setCheckable(False)
        self.btn_save_net_param.setObjectName("btn_save_net_param")
        self.gridLayout.addWidget(self.btn_save_net_param, 10, 2, 1, 1)
        self.label_device_mac_addr = SubtitleLabel(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_device_mac_addr.sizePolicy().hasHeightForWidth())
        self.label_device_mac_addr.setSizePolicy(sizePolicy)
        self.label_device_mac_addr.setMinimumSize(QtCore.QSize(0, 0))
        self.label_device_mac_addr.setText("")
        self.label_device_mac_addr.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_device_mac_addr.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByMouse|QtCore.Qt.TextSelectableByMouse)
        self.label_device_mac_addr.setObjectName("label_device_mac_addr")
        self.gridLayout.addWidget(self.label_device_mac_addr, 8, 2, 1, 1)
        self.gridLayout_2.addWidget(self.CardWidget, 1, 0, 1, 3)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.LargeTitleLabel.setText(_translate("Form", "电机控制器"))
        self.label_1.setText(_translate("Form", "本机IP地址："))
        self.label_pc_ip.setText(_translate("Form", "***********"))
        self.line_edit_pc_ip.setPlaceholderText(_translate("Form", "***********"))
        self.label_3.setText(_translate("Form", "本机端口："))
        self.label_pc_port.setText(_translate("Form", "16011"))
        self.label_2.setText(_translate("Form", "设备IP地址："))
        self.label_device_ip.setText(_translate("Form", "***********"))
        self.line_edit_device_ip.setPlaceholderText(_translate("Form", "***********00"))
        self.label_4.setText(_translate("Form", "设备端口："))
        self.label_device_port.setText(_translate("Form", "16011"))
        self.label_8.setText(_translate("Form", "设备子网掩码："))
        self.label_device_ip_mask.setText(_translate("Form", "*************"))
        self.line_edit_device_ip_mask.setText(_translate("Form", "*************"))
        self.label_6.setText(_translate("Form", "设备别名："))
        self.label_device_name.setText(_translate("Form", "一号驱动器"))
        self.line_edit_device_name.setText(_translate("Form", "一号驱动器"))
        self.label_5.setText(_translate("Form", "设备UID："))
        self.label_device_uid.setText(_translate("Form", "0x12345678"))
        self.label_9.setText(_translate("Form", "设备MAC地址："))
        self.label_7.setText(_translate("Form", "永久保存参数："))
        self.btn_save_net_param.setText(_translate("Form", "保存网络参数命令"))
from qfluentwidgets import CardWidget, LargeTitleLabel, LineEdit, PillPushButton, SpinBox, SubtitleLabel, ToolButton
