# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src\ui\device_setting.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1138, 823)
        self.gridLayout_3 = QtWidgets.QGridLayout(Form)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.SubtitleLabel = SubtitleLabel(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SubtitleLabel.sizePolicy().hasHeightForWidth())
        self.SubtitleLabel.setSizePolicy(sizePolicy)
        self.SubtitleLabel.setMinimumSize(QtCore.QSize(0, 50))
        self.SubtitleLabel.setMaximumSize(QtCore.QSize(16777215, 50))
        self.SubtitleLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.SubtitleLabel.setObjectName("SubtitleLabel")
        self.gridLayout_3.addWidget(self.SubtitleLabel, 0, 0, 1, 1)
        self.CardWidget = CardWidget(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.CardWidget.sizePolicy().hasHeightForWidth())
        self.CardWidget.setSizePolicy(sizePolicy)
        self.CardWidget.setObjectName("CardWidget")
        self.gridLayout = QtWidgets.QGridLayout(self.CardWidget)
        self.gridLayout.setObjectName("gridLayout")
        self.table_pid_param = TableWidget(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.table_pid_param.sizePolicy().hasHeightForWidth())
        self.table_pid_param.setSizePolicy(sizePolicy)
        self.table_pid_param.setMinimumSize(QtCore.QSize(440, 0))
        font = QtGui.QFont()
        font.setPointSize(12)
        self.table_pid_param.setFont(font)
        self.table_pid_param.setSelectRightClickedRow(True)
        self.table_pid_param.setObjectName("table_pid_param")
        self.table_pid_param.setColumnCount(0)
        self.table_pid_param.setRowCount(0)
        self.gridLayout.addWidget(self.table_pid_param, 1, 1, 1, 1)
        self.table_other_param = TableWidget(self.CardWidget)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.table_other_param.setFont(font)
        self.table_other_param.setSelectRightClickedRow(True)
        self.table_other_param.setObjectName("table_other_param")
        self.table_other_param.setColumnCount(0)
        self.table_other_param.setRowCount(0)
        self.gridLayout.addWidget(self.table_other_param, 2, 1, 1, 1)
        self.gridLayout_3.addWidget(self.CardWidget, 1, 0, 1, 1)
        self.CardWidget_2 = CardWidget(Form)
        self.CardWidget_2.setObjectName("CardWidget_2")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.CardWidget_2)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.btn_Rottx_Zero_Current = ToolButton(self.CardWidget_2)
        self.btn_Rottx_Zero_Current.setMinimumSize(QtCore.QSize(44, 44))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap("src\\ui\\../../img/save2.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_Rottx_Zero_Current.setIcon(icon)
        self.btn_Rottx_Zero_Current.setIconSize(QtCore.QSize(22, 22))
        self.btn_Rottx_Zero_Current.setObjectName("btn_Rottx_Zero_Current")
        self.gridLayout_2.addWidget(self.btn_Rottx_Zero_Current, 5, 2, 1, 1)
        self.spinbox_id_pid_i = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_id_pid_i.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_id_pid_i.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_id_pid_i.setDecimals(6)
        self.spinbox_id_pid_i.setMinimum(-50.0)
        self.spinbox_id_pid_i.setMaximum(50.0)
        self.spinbox_id_pid_i.setSingleStep(0.0001)
        self.spinbox_id_pid_i.setObjectName("spinbox_id_pid_i")
        self.gridLayout_2.addWidget(self.spinbox_id_pid_i, 2, 3, 1, 1)
        self.label_speed_15 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_15.sizePolicy().hasHeightForWidth())
        self.label_speed_15.setSizePolicy(sizePolicy)
        self.label_speed_15.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_15.setText("")
        self.label_speed_15.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_15.setObjectName("label_speed_15")
        self.gridLayout_2.addWidget(self.label_speed_15, 12, 1, 1, 2)
        self.spinbox_speed_pid_i = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_speed_pid_i.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_speed_pid_i.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_speed_pid_i.setDecimals(6)
        self.spinbox_speed_pid_i.setMinimum(-50.0)
        self.spinbox_speed_pid_i.setMaximum(50.0)
        self.spinbox_speed_pid_i.setSingleStep(0.0001)
        self.spinbox_speed_pid_i.setObjectName("spinbox_speed_pid_i")
        self.gridLayout_2.addWidget(self.spinbox_speed_pid_i, 1, 3, 1, 1)
        self.btn_Idref = ToolButton(self.CardWidget_2)
        self.btn_Idref.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_Idref.setIcon(icon)
        self.btn_Idref.setIconSize(QtCore.QSize(22, 22))
        self.btn_Idref.setObjectName("btn_Idref")
        self.gridLayout_2.addWidget(self.btn_Idref, 9, 2, 1, 1)
        self.spinbox_Iqref = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Iqref.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Iqref.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Iqref.setDecimals(6)
        self.spinbox_Iqref.setMinimum(-1000.0)
        self.spinbox_Iqref.setMaximum(1000.0)
        self.spinbox_Iqref.setSingleStep(1.0)
        self.spinbox_Iqref.setObjectName("spinbox_Iqref")
        self.gridLayout_2.addWidget(self.spinbox_Iqref, 9, 3, 1, 1)
        self.btn_Iset_d_ref = ToolButton(self.CardWidget_2)
        self.btn_Iset_d_ref.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_Iset_d_ref.setIcon(icon)
        self.btn_Iset_d_ref.setIconSize(QtCore.QSize(22, 22))
        self.btn_Iset_d_ref.setObjectName("btn_Iset_d_ref")
        self.gridLayout_2.addWidget(self.btn_Iset_d_ref, 4, 4, 1, 1)
        self.btn_Vdcset_ref = ToolButton(self.CardWidget_2)
        self.btn_Vdcset_ref.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_Vdcset_ref.setIcon(icon)
        self.btn_Vdcset_ref.setIconSize(QtCore.QSize(22, 22))
        self.btn_Vdcset_ref.setObjectName("btn_Vdcset_ref")
        self.gridLayout_2.addWidget(self.btn_Vdcset_ref, 4, 2, 1, 1)
        self.spinbox_id_pid_p = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_id_pid_p.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_id_pid_p.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_id_pid_p.setDecimals(6)
        self.spinbox_id_pid_p.setMinimum(-50.0)
        self.spinbox_id_pid_p.setMaximum(50.0)
        self.spinbox_id_pid_p.setSingleStep(0.0001)
        self.spinbox_id_pid_p.setObjectName("spinbox_id_pid_p")
        self.gridLayout_2.addWidget(self.spinbox_id_pid_p, 2, 1, 1, 1)
        self.label_speed_2 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_2.sizePolicy().hasHeightForWidth())
        self.label_speed_2.setSizePolicy(sizePolicy)
        self.label_speed_2.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_speed_2.setObjectName("label_speed_2")
        self.gridLayout_2.addWidget(self.label_speed_2, 0, 1, 1, 1)
        self.label_speed_ctrl_mode_select = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_ctrl_mode_select.sizePolicy().hasHeightForWidth())
        self.label_speed_ctrl_mode_select.setSizePolicy(sizePolicy)
        self.label_speed_ctrl_mode_select.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_ctrl_mode_select.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_ctrl_mode_select.setObjectName("label_speed_ctrl_mode_select")
        self.gridLayout_2.addWidget(self.label_speed_ctrl_mode_select, 10, 1, 1, 2)
        self.spinbox_speed_pid_p = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_speed_pid_p.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_speed_pid_p.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_speed_pid_p.setDecimals(6)
        self.spinbox_speed_pid_p.setMinimum(-50.0)
        self.spinbox_speed_pid_p.setMaximum(50.0)
        self.spinbox_speed_pid_p.setSingleStep(0.0001)
        self.spinbox_speed_pid_p.setObjectName("spinbox_speed_pid_p")
        self.gridLayout_2.addWidget(self.spinbox_speed_pid_p, 1, 1, 1, 1)
        self.spinbox_Iset_d_ref = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Iset_d_ref.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Iset_d_ref.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Iset_d_ref.setDecimals(6)
        self.spinbox_Iset_d_ref.setMinimum(0.0)
        self.spinbox_Iset_d_ref.setMaximum(1000.0)
        self.spinbox_Iset_d_ref.setSingleStep(10.0)
        self.spinbox_Iset_d_ref.setObjectName("spinbox_Iset_d_ref")
        self.gridLayout_2.addWidget(self.spinbox_Iset_d_ref, 4, 3, 1, 1)
        self.spinbox_Motor_Resolver_Zero = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Motor_Resolver_Zero.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Motor_Resolver_Zero.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Motor_Resolver_Zero.setDecimals(6)
        self.spinbox_Motor_Resolver_Zero.setMinimum(-1000.0)
        self.spinbox_Motor_Resolver_Zero.setMaximum(10000.0)
        self.spinbox_Motor_Resolver_Zero.setSingleStep(0.0001)
        self.spinbox_Motor_Resolver_Zero.setObjectName("spinbox_Motor_Resolver_Zero")
        self.gridLayout_2.addWidget(self.spinbox_Motor_Resolver_Zero, 6, 1, 1, 1)
        self.label_speed_5 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_5.sizePolicy().hasHeightForWidth())
        self.label_speed_5.setSizePolicy(sizePolicy)
        self.label_speed_5.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_5.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_5.setObjectName("label_speed_5")
        self.gridLayout_2.addWidget(self.label_speed_5, 2, 0, 1, 1)
        self.spinbox_Vdcset_ref = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Vdcset_ref.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Vdcset_ref.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Vdcset_ref.setDecimals(6)
        self.spinbox_Vdcset_ref.setMinimum(0.0)
        self.spinbox_Vdcset_ref.setMaximum(1000.0)
        self.spinbox_Vdcset_ref.setSingleStep(10.0)
        self.spinbox_Vdcset_ref.setObjectName("spinbox_Vdcset_ref")
        self.gridLayout_2.addWidget(self.spinbox_Vdcset_ref, 4, 1, 1, 1)
        self.btn_Iqref = ToolButton(self.CardWidget_2)
        self.btn_Iqref.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_Iqref.setIcon(icon)
        self.btn_Iqref.setIconSize(QtCore.QSize(22, 22))
        self.btn_Iqref.setObjectName("btn_Iqref")
        self.gridLayout_2.addWidget(self.btn_Iqref, 9, 4, 1, 1)
        self.btn_save_param = PillPushButton(self.CardWidget_2)
        self.btn_save_param.setMinimumSize(QtCore.QSize(0, 44))
        self.btn_save_param.setCheckable(False)
        self.btn_save_param.setObjectName("btn_save_param")
        self.gridLayout_2.addWidget(self.btn_save_param, 12, 3, 1, 1)
        self.label_speed = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed.sizePolicy().hasHeightForWidth())
        self.label_speed.setSizePolicy(sizePolicy)
        self.label_speed.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed.setObjectName("label_speed")
        self.gridLayout_2.addWidget(self.label_speed, 1, 0, 1, 1)
        self.btn_Motor_Resolver_Zero = ToolButton(self.CardWidget_2)
        self.btn_Motor_Resolver_Zero.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_Motor_Resolver_Zero.setIcon(icon)
        self.btn_Motor_Resolver_Zero.setIconSize(QtCore.QSize(22, 22))
        self.btn_Motor_Resolver_Zero.setObjectName("btn_Motor_Resolver_Zero")
        self.gridLayout_2.addWidget(self.btn_Motor_Resolver_Zero, 6, 2, 1, 1)
        self.btn_Motor_Id_Min = ToolButton(self.CardWidget_2)
        self.btn_Motor_Id_Min.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_Motor_Id_Min.setIcon(icon)
        self.btn_Motor_Id_Min.setIconSize(QtCore.QSize(22, 22))
        self.btn_Motor_Id_Min.setObjectName("btn_Motor_Id_Min")
        self.gridLayout_2.addWidget(self.btn_Motor_Id_Min, 8, 4, 1, 1)
        self.spinbox_Motor_Pn = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Motor_Pn.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Motor_Pn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Motor_Pn.setDecimals(0)
        self.spinbox_Motor_Pn.setMinimum(1.0)
        self.spinbox_Motor_Pn.setMaximum(24.0)
        self.spinbox_Motor_Pn.setSingleStep(1.0)
        self.spinbox_Motor_Pn.setObjectName("spinbox_Motor_Pn")
        self.gridLayout_2.addWidget(self.spinbox_Motor_Pn, 7, 1, 1, 1)
        self.btn_iq_pid_i = ToolButton(self.CardWidget_2)
        self.btn_iq_pid_i.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_iq_pid_i.setIcon(icon)
        self.btn_iq_pid_i.setIconSize(QtCore.QSize(22, 22))
        self.btn_iq_pid_i.setObjectName("btn_iq_pid_i")
        self.gridLayout_2.addWidget(self.btn_iq_pid_i, 3, 4, 1, 1)
        self.label_speed_19 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_19.sizePolicy().hasHeightForWidth())
        self.label_speed_19.setSizePolicy(sizePolicy)
        self.label_speed_19.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_19.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_19.setObjectName("label_speed_19")
        self.gridLayout_2.addWidget(self.label_speed_19, 8, 0, 1, 1)
        self.spinbox_Rottx_Zero_Current = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Rottx_Zero_Current.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Rottx_Zero_Current.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Rottx_Zero_Current.setDecimals(6)
        self.spinbox_Rottx_Zero_Current.setMinimum(-1000.0)
        self.spinbox_Rottx_Zero_Current.setMaximum(10000.0)
        self.spinbox_Rottx_Zero_Current.setSingleStep(0.0001)
        self.spinbox_Rottx_Zero_Current.setObjectName("spinbox_Rottx_Zero_Current")
        self.gridLayout_2.addWidget(self.spinbox_Rottx_Zero_Current, 5, 1, 1, 1)
        self.label_start_resolver_calibration = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_start_resolver_calibration.sizePolicy().hasHeightForWidth())
        self.label_start_resolver_calibration.setSizePolicy(sizePolicy)
        self.label_start_resolver_calibration.setMinimumSize(QtCore.QSize(0, 0))
        self.label_start_resolver_calibration.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_start_resolver_calibration.setObjectName("label_start_resolver_calibration")
        self.gridLayout_2.addWidget(self.label_start_resolver_calibration, 11, 1, 1, 2)
        self.btn_Motor_Id_Max = ToolButton(self.CardWidget_2)
        self.btn_Motor_Id_Max.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_Motor_Id_Max.setIcon(icon)
        self.btn_Motor_Id_Max.setIconSize(QtCore.QSize(22, 22))
        self.btn_Motor_Id_Max.setObjectName("btn_Motor_Id_Max")
        self.gridLayout_2.addWidget(self.btn_Motor_Id_Max, 8, 2, 1, 1)
        self.label_speed_14 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_14.sizePolicy().hasHeightForWidth())
        self.label_speed_14.setSizePolicy(sizePolicy)
        self.label_speed_14.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_14.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_14.setObjectName("label_speed_14")
        self.gridLayout_2.addWidget(self.label_speed_14, 12, 0, 1, 1)
        self.label_speed_20 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_20.sizePolicy().hasHeightForWidth())
        self.label_speed_20.setSizePolicy(sizePolicy)
        self.label_speed_20.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_20.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_20.setObjectName("label_speed_20")
        self.gridLayout_2.addWidget(self.label_speed_20, 9, 0, 1, 1)
        self.label_speed_7 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_7.sizePolicy().hasHeightForWidth())
        self.label_speed_7.setSizePolicy(sizePolicy)
        self.label_speed_7.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_7.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_7.setObjectName("label_speed_7")
        self.gridLayout_2.addWidget(self.label_speed_7, 4, 0, 1, 1)
        self.btn_speed_pid_i = ToolButton(self.CardWidget_2)
        self.btn_speed_pid_i.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_speed_pid_i.setIcon(icon)
        self.btn_speed_pid_i.setIconSize(QtCore.QSize(22, 22))
        self.btn_speed_pid_i.setObjectName("btn_speed_pid_i")
        self.gridLayout_2.addWidget(self.btn_speed_pid_i, 1, 4, 1, 1)
        self.btn_ctrl_mode_select = PillPushButton(self.CardWidget_2)
        self.btn_ctrl_mode_select.setMinimumSize(QtCore.QSize(0, 44))
        self.btn_ctrl_mode_select.setCheckable(True)
        self.btn_ctrl_mode_select.setObjectName("btn_ctrl_mode_select")
        self.gridLayout_2.addWidget(self.btn_ctrl_mode_select, 10, 3, 1, 1)
        self.btn_start_resolver_calibration = PillPushButton(self.CardWidget_2)
        self.btn_start_resolver_calibration.setMinimumSize(QtCore.QSize(0, 44))
        self.btn_start_resolver_calibration.setCheckable(False)
        self.btn_start_resolver_calibration.setObjectName("btn_start_resolver_calibration")
        self.gridLayout_2.addWidget(self.btn_start_resolver_calibration, 11, 3, 1, 1)
        self.spinbox_Motor_Id_Min = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Motor_Id_Min.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Motor_Id_Min.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Motor_Id_Min.setDecimals(6)
        self.spinbox_Motor_Id_Min.setMinimum(-1000.0)
        self.spinbox_Motor_Id_Min.setMaximum(1000.0)
        self.spinbox_Motor_Id_Min.setSingleStep(1.0)
        self.spinbox_Motor_Id_Min.setObjectName("spinbox_Motor_Id_Min")
        self.gridLayout_2.addWidget(self.spinbox_Motor_Id_Min, 8, 3, 1, 1)
        self.label_speed_17 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_17.sizePolicy().hasHeightForWidth())
        self.label_speed_17.setSizePolicy(sizePolicy)
        self.label_speed_17.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_17.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_17.setObjectName("label_speed_17")
        self.gridLayout_2.addWidget(self.label_speed_17, 6, 0, 1, 1)
        self.label_speed_3 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_3.sizePolicy().hasHeightForWidth())
        self.label_speed_3.setSizePolicy(sizePolicy)
        self.label_speed_3.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_3.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_speed_3.setObjectName("label_speed_3")
        self.gridLayout_2.addWidget(self.label_speed_3, 0, 3, 1, 1)
        self.spinbox_iq_pid_i = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_iq_pid_i.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_iq_pid_i.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_iq_pid_i.setDecimals(6)
        self.spinbox_iq_pid_i.setMinimum(-50.0)
        self.spinbox_iq_pid_i.setMaximum(50.0)
        self.spinbox_iq_pid_i.setSingleStep(0.0001)
        self.spinbox_iq_pid_i.setObjectName("spinbox_iq_pid_i")
        self.gridLayout_2.addWidget(self.spinbox_iq_pid_i, 3, 3, 1, 1)
        self.label_speed_16 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_16.sizePolicy().hasHeightForWidth())
        self.label_speed_16.setSizePolicy(sizePolicy)
        self.label_speed_16.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_16.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_16.setObjectName("label_speed_16")
        self.gridLayout_2.addWidget(self.label_speed_16, 5, 0, 1, 1)
        self.btn_id_pid_p = ToolButton(self.CardWidget_2)
        self.btn_id_pid_p.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_id_pid_p.setIcon(icon)
        self.btn_id_pid_p.setIconSize(QtCore.QSize(22, 22))
        self.btn_id_pid_p.setObjectName("btn_id_pid_p")
        self.gridLayout_2.addWidget(self.btn_id_pid_p, 2, 2, 1, 1)
        self.btn_Motor_Pn = ToolButton(self.CardWidget_2)
        self.btn_Motor_Pn.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_Motor_Pn.setIcon(icon)
        self.btn_Motor_Pn.setIconSize(QtCore.QSize(22, 22))
        self.btn_Motor_Pn.setObjectName("btn_Motor_Pn")
        self.gridLayout_2.addWidget(self.btn_Motor_Pn, 7, 2, 1, 1)
        self.label_speed_6 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_6.sizePolicy().hasHeightForWidth())
        self.label_speed_6.setSizePolicy(sizePolicy)
        self.label_speed_6.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_6.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_6.setObjectName("label_speed_6")
        self.gridLayout_2.addWidget(self.label_speed_6, 3, 0, 1, 1)
        self.label_speed_18 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_18.sizePolicy().hasHeightForWidth())
        self.label_speed_18.setSizePolicy(sizePolicy)
        self.label_speed_18.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_18.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_18.setObjectName("label_speed_18")
        self.gridLayout_2.addWidget(self.label_speed_18, 7, 0, 1, 1)
        self.label_speed_12 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_12.sizePolicy().hasHeightForWidth())
        self.label_speed_12.setSizePolicy(sizePolicy)
        self.label_speed_12.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_12.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_12.setObjectName("label_speed_12")
        self.gridLayout_2.addWidget(self.label_speed_12, 11, 0, 1, 1)
        self.btn_id_pid_i = ToolButton(self.CardWidget_2)
        self.btn_id_pid_i.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_id_pid_i.setIcon(icon)
        self.btn_id_pid_i.setIconSize(QtCore.QSize(22, 22))
        self.btn_id_pid_i.setObjectName("btn_id_pid_i")
        self.gridLayout_2.addWidget(self.btn_id_pid_i, 2, 4, 1, 1)
        self.spinbox_Motor_Id_Max = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Motor_Id_Max.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Motor_Id_Max.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Motor_Id_Max.setDecimals(6)
        self.spinbox_Motor_Id_Max.setMinimum(-1000.0)
        self.spinbox_Motor_Id_Max.setMaximum(1000.0)
        self.spinbox_Motor_Id_Max.setSingleStep(1.0)
        self.spinbox_Motor_Id_Max.setObjectName("spinbox_Motor_Id_Max")
        self.gridLayout_2.addWidget(self.spinbox_Motor_Id_Max, 8, 1, 1, 1)
        self.label_speed_8 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_8.sizePolicy().hasHeightForWidth())
        self.label_speed_8.setSizePolicy(sizePolicy)
        self.label_speed_8.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_8.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_8.setObjectName("label_speed_8")
        self.gridLayout_2.addWidget(self.label_speed_8, 10, 0, 1, 1)
        self.spinbox_Idref = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_Idref.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_Idref.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_Idref.setDecimals(6)
        self.spinbox_Idref.setMinimum(-1000.0)
        self.spinbox_Idref.setMaximum(1000.0)
        self.spinbox_Idref.setSingleStep(1.0)
        self.spinbox_Idref.setObjectName("spinbox_Idref")
        self.gridLayout_2.addWidget(self.spinbox_Idref, 9, 1, 1, 1)
        self.spinbox_iq_pid_p = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_iq_pid_p.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_iq_pid_p.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_iq_pid_p.setDecimals(6)
        self.spinbox_iq_pid_p.setMinimum(-50.0)
        self.spinbox_iq_pid_p.setMaximum(50.0)
        self.spinbox_iq_pid_p.setSingleStep(0.0001)
        self.spinbox_iq_pid_p.setObjectName("spinbox_iq_pid_p")
        self.gridLayout_2.addWidget(self.spinbox_iq_pid_p, 3, 1, 1, 1)
        self.btn_iq_pid_p = ToolButton(self.CardWidget_2)
        self.btn_iq_pid_p.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_iq_pid_p.setIcon(icon)
        self.btn_iq_pid_p.setIconSize(QtCore.QSize(22, 22))
        self.btn_iq_pid_p.setObjectName("btn_iq_pid_p")
        self.gridLayout_2.addWidget(self.btn_iq_pid_p, 3, 2, 1, 1)
        self.btn_speed_pid_p = ToolButton(self.CardWidget_2)
        self.btn_speed_pid_p.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_speed_pid_p.setIcon(icon)
        self.btn_speed_pid_p.setIconSize(QtCore.QSize(22, 22))
        self.btn_speed_pid_p.setObjectName("btn_speed_pid_p")
        self.gridLayout_2.addWidget(self.btn_speed_pid_p, 1, 2, 1, 1)
        self.gridLayout_3.addWidget(self.CardWidget_2, 1, 2, 1, 1)
        self.SubtitleLabel_2 = SubtitleLabel(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SubtitleLabel_2.sizePolicy().hasHeightForWidth())
        self.SubtitleLabel_2.setSizePolicy(sizePolicy)
        self.SubtitleLabel_2.setMinimumSize(QtCore.QSize(0, 50))
        self.SubtitleLabel_2.setMaximumSize(QtCore.QSize(16777215, 50))
        self.SubtitleLabel_2.setBaseSize(QtCore.QSize(0, 50))
        self.SubtitleLabel_2.setAlignment(QtCore.Qt.AlignCenter)
        self.SubtitleLabel_2.setObjectName("SubtitleLabel_2")
        self.gridLayout_3.addWidget(self.SubtitleLabel_2, 0, 2, 1, 1)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.SubtitleLabel.setText(_translate("Form", "参数监控"))
        self.spinbox_Iqref.setPrefix(_translate("Form", "Iq: "))
        self.label_speed_2.setText(_translate("Form", "P"))
        self.label_speed_ctrl_mode_select.setText(_translate("Form", "当前为速度环模式"))
        self.spinbox_Iset_d_ref.setSuffix(_translate("Form", " A"))
        self.label_speed_5.setText(_translate("Form", "电流环D："))
        self.spinbox_Vdcset_ref.setSuffix(_translate("Form", " V"))
        self.btn_save_param.setText(_translate("Form", "保存参数命令"))
        self.label_speed.setText(_translate("Form", "速度环："))
        self.label_speed_19.setText(_translate("Form", "Motor_Id上下限："))
        self.label_start_resolver_calibration.setText(_translate("Form", "当前未启动旋变校准"))
        self.label_speed_14.setText(_translate("Form", "永久保存参数："))
        self.label_speed_20.setText(_translate("Form", "Idref/Iqref："))
        self.label_speed_7.setText(_translate("Form", "母线电压/电流参考："))
        self.btn_ctrl_mode_select.setText(_translate("Form", "切换模式命令"))
        self.btn_start_resolver_calibration.setText(_translate("Form", "启动旋变校准命令"))
        self.spinbox_Motor_Id_Min.setPrefix(_translate("Form", "Min: "))
        self.label_speed_17.setText(_translate("Form", "Motor_Resolver_Zero："))
        self.label_speed_3.setText(_translate("Form", "I"))
        self.label_speed_16.setText(_translate("Form", "Rottx_Zero_Current："))
        self.label_speed_6.setText(_translate("Form", "电流环Q：     "))
        self.label_speed_18.setText(_translate("Form", "Motor_Pn："))
        self.label_speed_12.setText(_translate("Form", "旋变零点校准："))
        self.spinbox_Motor_Id_Max.setPrefix(_translate("Form", "Max: "))
        self.label_speed_8.setText(_translate("Form", "控制模式选择："))
        self.spinbox_Idref.setPrefix(_translate("Form", "Id: "))
        self.SubtitleLabel_2.setText(_translate("Form", "参数设置"))
from qfluentwidgets import CardWidget, DoubleSpinBox, PillPushButton, SubtitleLabel, TableWidget, ToolButton
