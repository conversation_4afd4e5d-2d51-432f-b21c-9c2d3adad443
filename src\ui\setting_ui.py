# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src\ui\setting.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(944, 604)
        self.gridLayout_2 = QtWidgets.QGridLayout(Form)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.LargeTitleLabel = LargeTitleLabel(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.LargeTitleLabel.sizePolicy().hasHeightForWidth())
        self.LargeTitleLabel.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("华文行楷")
        font.setPointSize(44)
        font.setBold(False)
        font.setWeight(50)
        self.LargeTitleLabel.setFont(font)
        self.LargeTitleLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.LargeTitleLabel.setObjectName("LargeTitleLabel")
        self.gridLayout_2.addWidget(self.LargeTitleLabel, 0, 0, 1, 1)
        self.CardWidget = CardWidget(Form)
        self.CardWidget.setObjectName("CardWidget")
        self.gridLayout = QtWidgets.QGridLayout(self.CardWidget)
        self.gridLayout.setObjectName("gridLayout")
        self.sw_theme = SwitchButton(self.CardWidget)
        self.sw_theme.setObjectName("sw_theme")
        self.gridLayout.addWidget(self.sw_theme, 0, 0, 1, 1)
        self.sw_scope = SwitchButton(self.CardWidget)
        self.sw_scope.setObjectName("sw_scope")
        self.gridLayout.addWidget(self.sw_scope, 1, 0, 1, 1)
        self.gridLayout_2.addWidget(self.CardWidget, 1, 0, 1, 1)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.LargeTitleLabel.setText(_translate("Form", "电机控制器"))
        self.sw_theme.setText(_translate("Form", "白昼"))
        self.sw_theme.setOnText(_translate("Form", "黑夜"))
        self.sw_theme.setOffText(_translate("Form", "白昼"))
        self.sw_scope.setOnText(_translate("Form", "显示示波器"))
        self.sw_scope.setOffText(_translate("Form", "隐藏示波器"))
from qfluentwidgets import CardWidget, LargeTitleLabel, SwitchButton
