1. 我发现最大化后打开右侧菜单总是出现如下报错，以后注意出现的时机。


QWindowsWindow::setGeometry: Unable to set geometry 2560x1512+0+0 (frame: 2586x1538-13-13) on QWidgetWindow/"MainWindowClassWindow" on "\\.\DISPLAY1". Resulting geometry: 2560x1504+0+0 (frame: 2586x1530-13-13) margins: 13, 13, 13, 13 minimum size: 997x756 MINMAXINFO maxSize=0,0 maxpos=0,0 mintrack=2020,1538 maxtrack=0,0)

2. 其次是我单独使用
class MainWindow(FluentWindow)添加继承pg.GraphicsLayoutWidget没问题，但是我这个项目里面就会造成黑屏，很奇怪。