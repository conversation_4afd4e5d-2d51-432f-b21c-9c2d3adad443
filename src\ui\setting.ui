<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>944</width>
    <height>604</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <widget class="LargeTitleLabel" name="LargeTitleLabel">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>华文行楷</family>
       <pointsize>44</pointsize>
       <weight>50</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="text">
      <string>电机控制器</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="CardWidget" name="CardWidget">
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="SwitchButton" name="sw_theme">
        <property name="text">
         <string>白昼</string>
        </property>
        <property name="onText">
         <string>黑夜</string>
        </property>
        <property name="offText">
         <string>白昼</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="SwitchButton" name="sw_scope">
        <property name="onText">
         <string>显示示波器</string>
        </property>
        <property name="offText">
         <string>隐藏示波器</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SwitchButton</class>
   <extends>QWidget</extends>
   <header>qfluentwidgets</header>
  </customwidget>
  <customwidget>
   <class>CardWidget</class>
   <extends>QFrame</extends>
   <header>qfluentwidgets</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>LargeTitleLabel</class>
   <extends>QLabel</extends>
   <header>qfluentwidgets</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
